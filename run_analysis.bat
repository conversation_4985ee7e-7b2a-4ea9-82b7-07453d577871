@echo off
echo ========================================
echo 医疗数据分析工具
echo ========================================

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过

echo.
echo 正在检查依赖包...
python -c "import pandas, numpy, matplotlib, seaborn" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装依赖包...
    pip install pandas numpy matplotlib seaborn
    if %errorlevel% neq 0 (
        echo 依赖包安装失败，请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo 依赖包检查通过

echo.
echo 选择运行模式:
echo 1. 完整分析（包含中文图表）
echo 2. 简化分析（英文图表，避免字体问题）
echo 3. 仅修复字体问题
echo 4. 仅数据预处理
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo 运行完整分析...
    python run_analysis.py
) else if "%choice%"=="2" (
    echo 运行简化分析...
    python simple_preprocessing.py
    python simple_visualization.py
) else if "%choice%"=="3" (
    echo 修复字体问题...
    python fix_chinese_font.py
) else if "%choice%"=="4" (
    echo 运行数据预处理...
    python simple_preprocessing.py
) else (
    echo 无效选择
)

echo.
echo 分析完成！
pause
