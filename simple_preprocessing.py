#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版医疗数据预处理脚本
适用于基础的数据清洗和分析
"""

import pandas as pd
import numpy as np
import os

def load_and_preview_data():
    """加载并预览数据"""
    print("正在加载数据文件...")
    
    # 主要数据文件
    files = {
        'patients': 'dim_patient.csv',
        'doctors': 'dim_doctor.csv', 
        'consultations': 'fact_consultation.csv'
    }
    
    data = {}
    
    for name, filename in files.items():
        if os.path.exists(filename):
            try:
                df = pd.read_csv(filename, encoding='utf-8')
                data[name] = df
                print(f"✓ {filename}: {df.shape[0]} 行, {df.shape[1]} 列")
                
                # 显示前几行
                print(f"\n{filename} 前5行数据:")
                print(df.head())
                print(f"\n数据类型:")
                print(df.dtypes)
                print(f"\n缺失值统计:")
                print(df.isnull().sum())
                print("-" * 50)
                
            except Exception as e:
                print(f"✗ 加载 {filename} 失败: {e}")
        else:
            print(f"✗ 文件不存在: {filename}")
    
    return data

def basic_cleaning(data):
    """基础数据清洗"""
    cleaned_data = {}
    
    # 清洗患者数据
    if 'patients' in data:
        print("清洗患者数据...")
        df = data['patients'].copy()
        
        # 处理年龄
        df['age'] = pd.to_numeric(df['age'], errors='coerce')
        df = df[(df['age'] >= 0) & (df['age'] <= 120)]
        
        # 处理性别
        df['gender_code'] = df['gender'].map({'男': 'M', '女': 'F'})
        
        # 处理日期
        df['registration_date'] = pd.to_datetime(df['registration_date'], errors='coerce')
        
        cleaned_data['patients'] = df
        print(f"患者数据清洗完成，保留 {len(df)} 条记录")
    
    # 清洗医生数据
    if 'doctors' in data:
        print("清洗医生数据...")
        df = data['doctors'].copy()
        
        # 处理数值字段
        df['age'] = pd.to_numeric(df['age'], errors='coerce')
        df['years_of_experience'] = pd.to_numeric(df['years_of_experience'], errors='coerce')
        df['consultation_fee'] = pd.to_numeric(df['consultation_fee'], errors='coerce')
        df['rating'] = pd.to_numeric(df['rating'], errors='coerce')
        
        # 限制评分范围
        df['rating'] = df['rating'].clip(0, 5)
        
        cleaned_data['doctors'] = df
        print(f"医生数据清洗完成，保留 {len(df)} 条记录")
    
    # 清洗咨询数据
    if 'consultations' in data:
        print("清洗咨询数据...")
        df = data['consultations'].copy()
        
        # 处理日期时间
        df['consultation_date'] = pd.to_datetime(df['consultation_date'], errors='coerce')
        
        # 处理数值字段
        numeric_cols = ['duration_minutes', 'actual_fee', 'insurance_covered_amount', 'patient_paid_amount']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 添加时间特征
        df['hour'] = df['consultation_date'].dt.hour
        df['weekday'] = df['consultation_date'].dt.dayofweek
        df['month'] = df['consultation_date'].dt.month
        
        cleaned_data['consultations'] = df
        print(f"咨询数据清洗完成，保留 {len(df)} 条记录")
    
    return cleaned_data

def generate_basic_stats(data):
    """生成基础统计信息"""
    print("\n" + "="*50)
    print("数据统计摘要")
    print("="*50)
    
    # 患者统计
    if 'patients' in data:
        df = data['patients']
        print(f"\n【患者数据】")
        print(f"总数: {len(df)}")
        print(f"平均年龄: {df['age'].mean():.1f} 岁")
        print(f"性别分布:")
        print(df['gender'].value_counts())
        print(f"血型分布:")
        print(df['blood_type'].value_counts())
    
    # 医生统计
    if 'doctors' in data:
        df = data['doctors']
        print(f"\n【医生数据】")
        print(f"总数: {len(df)}")
        print(f"平均工作年限: {df['years_of_experience'].mean():.1f} 年")
        print(f"平均咨询费: {df['consultation_fee'].mean():.2f} 元")
        print(f"科室分布:")
        print(df['department'].value_counts().head())
    
    # 咨询统计
    if 'consultations' in data:
        df = data['consultations']
        print(f"\n【咨询数据】")
        print(f"总数: {len(df)}")
        print(f"平均时长: {df['duration_minutes'].mean():.1f} 分钟")
        print(f"平均费用: {df['actual_fee'].mean():.2f} 元")
        print(f"咨询类型分布:")
        print(df['consultation_type'].value_counts())
        print(f"状态分布:")
        print(df['status'].value_counts())

def save_cleaned_data(data):
    """保存清洗后的数据"""
    output_dir = 'cleaned_data'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print(f"\n保存清洗后的数据到 {output_dir} 目录...")
    
    for name, df in data.items():
        filename = f"{name}_cleaned.csv"
        filepath = os.path.join(output_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf-8')
        print(f"✓ 保存 {filename}")

def main():
    """主函数"""
    print("医疗数据简化预处理工具")
    print("="*50)
    
    try:
        # 1. 加载数据
        data = load_and_preview_data()
        
        if not data:
            print("没有找到可用的数据文件")
            return
        
        # 2. 数据清洗
        cleaned_data = basic_cleaning(data)
        
        # 3. 生成统计信息
        generate_basic_stats(cleaned_data)
        
        # 4. 保存清洗后的数据
        save_cleaned_data(cleaned_data)
        
        print("\n数据预处理完成！")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
