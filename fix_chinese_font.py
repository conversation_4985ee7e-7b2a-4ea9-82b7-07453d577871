#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文字体修复脚本
解决matplotlib中文显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def list_available_fonts():
    """列出系统中可用的字体"""
    print("正在检查系统字体...")
    font_list = [f.name for f in fm.fontManager.ttflist]
    
    # 查找中文字体
    chinese_fonts = []
    chinese_keywords = ['SimHei', 'Microsoft', 'YaHei', 'KaiTi', 'FangSong', 'STSong', 'STKaiti', 'Noto', 'WenQuanYi', 'Arial Unicode']
    
    for font in font_list:
        for keyword in chinese_keywords:
            if keyword in font:
                chinese_fonts.append(font)
                break
    
    print(f"系统类型: {platform.system()}")
    print(f"找到 {len(chinese_fonts)} 个可能的中文字体:")
    for font in set(chinese_fonts):
        print(f"  - {font}")
    
    return list(set(chinese_fonts))

def test_chinese_display():
    """测试中文显示效果"""
    print("\n正在测试中文显示...")
    
    # 获取可用的中文字体
    chinese_fonts = list_available_fonts()
    
    if not chinese_fonts:
        print("警告: 未找到中文字体，尝试使用默认字体")
        chinese_fonts = ['DejaVu Sans']
    
    # 设置字体
    best_font = chinese_fonts[0]
    plt.rcParams['font.sans-serif'] = [best_font]
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 测试数据
    categories = ['患者数据', '医生信息', '咨询记录', '支付信息', '处方数据']
    values = [502, 202, 10002, 8500, 6800]
    
    # 创建柱状图
    bars = ax.bar(categories, values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'lightpink'])
    
    # 设置标题和标签
    ax.set_title('中文字体显示测试', fontsize=16, pad=20)
    ax.set_xlabel('数据类型', fontsize=12)
    ax.set_ylabel('记录数量', fontsize=12)
    
    # 在柱子上显示数值
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 50,
                f'{value:,}', ha='center', va='bottom', fontsize=10)
    
    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    
    # 保存测试图片
    test_dir = 'font_test'
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    output_path = os.path.join(test_dir, 'chinese_font_test.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✓ 测试图表已保存到: {output_path}")
    print(f"✓ 使用字体: {best_font}")
    
    return best_font

def create_font_config():
    """创建字体配置文件"""
    best_font = test_chinese_display()
    
    config_content = f'''# matplotlib中文字体配置
# 由fix_chinese_font.py自动生成

import matplotlib.pyplot as plt
import platform

def setup_chinese_font():
    """设置中文字体"""
    try:
        # 推荐字体: {best_font}
        if platform.system() == 'Windows':
            plt.rcParams['font.sans-serif'] = ['{best_font}', 'Microsoft YaHei', 'SimHei', 'KaiTi']
        else:
            plt.rcParams['font.sans-serif'] = ['{best_font}', 'DejaVu Sans', 'Arial Unicode MS']
        
        plt.rcParams['axes.unicode_minus'] = False
        print(f"已设置中文字体: {best_font}")
        
    except Exception as e:
        print(f"字体设置失败: {{e}}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

# 自动执行字体设置
setup_chinese_font()
'''
    
    with open('font_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 字体配置文件已创建: font_config.py")

def fix_visualization_files():
    """修复可视化文件中的字体设置"""
    print("\n正在修复可视化文件...")
    
    # 在data_visualization.py开头添加字体配置导入
    import_line = "from font_config import setup_chinese_font\nsetup_chinese_font()\n"
    
    try:
        with open('data_visualization.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'from font_config import setup_chinese_font' not in content:
            # 在导入部分后添加字体配置
            lines = content.split('\n')
            insert_index = 0
            for i, line in enumerate(lines):
                if line.startswith('import') or line.startswith('from'):
                    insert_index = i + 1
            
            lines.insert(insert_index, '')
            lines.insert(insert_index + 1, '# 导入字体配置')
            lines.insert(insert_index + 2, 'from font_config import setup_chinese_font')
            lines.insert(insert_index + 3, 'setup_chinese_font()')
            lines.insert(insert_index + 4, '')
            
            with open('data_visualization.py', 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            print("✓ 已修复 data_visualization.py")
        else:
            print("✓ data_visualization.py 已包含字体配置")
            
    except Exception as e:
        print(f"修复 data_visualization.py 失败: {e}")

def main():
    """主函数"""
    print("="*50)
    print("中文字体修复工具")
    print("="*50)
    
    try:
        # 1. 检查可用字体
        available_fonts = list_available_fonts()
        
        # 2. 测试中文显示
        best_font = test_chinese_display()
        
        # 3. 创建字体配置文件
        create_font_config()
        
        # 4. 修复现有文件
        fix_visualization_files()
        
        print("\n" + "="*50)
        print("字体修复完成！")
        print("="*50)
        print("建议:")
        print("1. 查看生成的测试图片确认中文显示正常")
        print("2. 重新运行 python data_visualization.py")
        print("3. 如果仍有问题，请安装Microsoft YaHei字体")
        
    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
