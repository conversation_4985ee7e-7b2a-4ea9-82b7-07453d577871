#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗数据分析主程序
整合数据预处理和可视化分析功能
"""

import os
import sys
from data_preprocessing import MedicalDataPreprocessor
from data_visualization import MedicalDataVisualizer


def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("缺少以下依赖包，请先安装：")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False
    return True

def setup_chinese_font():
    """设置中文字体显示"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        import platform

        # 获取系统中可用的字体
        font_list = [f.name for f in fm.fontManager.ttflist]

        # 中文字体优先级列表
        chinese_fonts = [
            'Microsoft YaHei',
            'SimHei',
            'KaiTi',
            'FangSong',
            'STSong',
            'STKaiti',
            'STFangsong',
            'Noto Sans CJK SC',
            'WenQuanYi Micro Hei',
            'Arial Unicode MS',
            'DejaVu Sans'
        ]

        # 找到第一个可用的中文字体
        available_font = None
        for font in chinese_fonts:
            if font in font_list:
                available_font = font
                break

        if available_font:
            plt.rcParams['font.sans-serif'] = [available_font]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"✓ 设置中文字体: {available_font}")
            return True
        else:
            print("⚠ 警告: 未找到合适的中文字体，图表中文可能显示为方框")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            return False

    except Exception as e:
        print(f"字体设置失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("医疗数据分析系统")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查数据文件
    required_files = [
        'dim_patient.csv',
        'dim_doctor.csv', 
        'fact_consultation.csv'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("缺少以下数据文件：")
        for file in missing_files:
            print(f"  {file}")
        print("请确保数据文件在当前目录中")
        return
    
    try:
        # 步骤0: 设置中文字体
        print("\n步骤0: 设置中文字体")
        print("-" * 30)
        font_ok = setup_chinese_font()
        if not font_ok:
            print("建议运行: python fix_chinese_font.py 来修复字体问题")

        # 步骤1: 数据预处理
        print("\n步骤1: 数据预处理")
        print("-" * 30)
        preprocessor = MedicalDataPreprocessor()
        preprocessor.run_preprocessing()
        
        # 步骤2: 数据可视化
        print("\n步骤2: 数据可视化分析")
        print("-" * 30)
        visualizer = MedicalDataVisualizer()
        visualizer.run_visualization()
        
        print("\n" + "="*60)
        print("分析完成！")
        print("="*60)
        print("输出文件：")
        print("• 处理后的数据: processed_data/ 目录")
        print("• 可视化图表: visualizations/ 目录")
        
    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
