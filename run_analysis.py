#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗数据分析主程序
整合数据预处理和可视化分析功能
"""

import os
import sys
from data_preprocessing import MedicalDataPreprocessor
from data_visualization import MedicalDataVisualizer

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包，请先安装：")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False
    return True

def main():
    """主函数"""
    print("="*60)
    print("医疗数据分析系统")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查数据文件
    required_files = [
        'dim_patient.csv',
        'dim_doctor.csv', 
        'fact_consultation.csv'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("缺少以下数据文件：")
        for file in missing_files:
            print(f"  {file}")
        print("请确保数据文件在当前目录中")
        return
    
    try:
        # 步骤1: 数据预处理
        print("\n步骤1: 数据预处理")
        print("-" * 30)
        preprocessor = MedicalDataPreprocessor()
        preprocessor.run_preprocessing()
        
        # 步骤2: 数据可视化
        print("\n步骤2: 数据可视化分析")
        print("-" * 30)
        visualizer = MedicalDataVisualizer()
        visualizer.run_visualization()
        
        print("\n" + "="*60)
        print("分析完成！")
        print("="*60)
        print("输出文件：")
        print("• 处理后的数据: processed_data/ 目录")
        print("• 可视化图表: visualizations/ 目录")
        
    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
