# 医疗数据预处理与分析系统

这是一个专门用于处理医疗咨询平台数据的Python工具集，包含数据预处理、清洗、统计分析和可视化功能。

## 功能特性

### 数据预处理 (`data_preprocessing.py`)
- 自动加载所有CSV数据文件
- 数据清洗和格式标准化
- 异常值检测和处理
- 缺失值填充
- 数据类型转换
- 生成数据质量报告

### 数据可视化 (`data_visualization.py`)
- 患者数据分析图表
- 医生数据分析图表
- 咨询业务分析图表
- 时间序列分析
- 统计摘要报告

### 一键运行 (`run_analysis.py`)
- 整合所有分析功能
- 自动检查依赖和数据文件
- 生成完整的分析报告

## 数据文件说明

系统支持以下数据文件：
- `dim_patient.csv` - 患者维度表
- `dim_doctor.csv` - 医生维度表
- `dim_hospital.csv` - 医院维度表
- `dim_medicine.csv` - 药品维度表
- `dim_user.csv` - 用户维度表
- `fact_consultation.csv` - 咨询事实表
- `fact_payment.csv` - 支付事实表
- `fact_prescription.csv` - 处方事实表
- `fact_prescription_detail.csv` - 处方明细表

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行完整分析
```bash
python run_analysis.py
```

### 3. 单独运行模块

#### 仅数据预处理
```bash
python data_preprocessing.py
```

#### 仅数据可视化（需要先运行预处理）
```bash
python data_visualization.py
```

## 输出文件

### 处理后的数据
- `processed_data/` 目录包含清洗后的CSV文件
- 文件命名格式：`{原文件名}_processed.csv`

### 可视化图表
- `visualizations/` 目录包含所有生成的图表
- `patient_analysis.png` - 患者数据分析
- `doctor_analysis.png` - 医生数据分析
- `consultation_analysis.png` - 咨询数据分析

## 主要处理内容

### 患者数据处理
- 年龄异常值处理（0-120岁范围）
- 性别编码标准化
- 日期格式统一
- 医保类型缺失值填充
- 过敏信息标准化

### 医生数据处理
- 工作年限和年龄验证
- 咨询费用异常值处理
- 评分范围限制（0-5分）
- 职称和科室标准化

### 咨询数据处理
- 日期时间格式统一
- 费用字段数值化处理
- 评分范围验证
- 时间维度特征提取（小时、星期、月份）

## 统计分析内容

- 基础统计信息（均值、中位数、分布等）
- 数据质量评估（缺失值、重复值、异常值）
- 业务指标分析（咨询完成率、平均费用等）
- 时间趋势分析
- 分类数据分布分析

## 中文字体显示问题解决方案

如果图表中的中文显示为方框，请按以下步骤解决：

### 方法1：自动修复（推荐）
```bash
python fix_chinese_font.py
```
这个脚本会：
- 检测系统可用的中文字体
- 生成字体测试图表
- 自动配置最佳字体设置

### 方法2：使用简化版可视化
```bash
python simple_visualization.py
```
这个版本使用英文标签，避免中文显示问题

### 方法3：手动安装字体
- **Windows**: 确保安装了"Microsoft YaHei"或"SimHei"字体
- **Linux**: 安装中文字体包 `sudo apt-get install fonts-wqy-microhei`
- **Mac**: 系统通常已包含中文字体

## 注意事项

1. 确保所有CSV文件使用UTF-8编码
2. 数据文件应放在脚本同一目录下
3. 运行前请确保有足够的磁盘空间存储输出文件
4. 如果遇到中文显示问题，请运行 `python fix_chinese_font.py`

## 系统要求

- Python 3.7+
- pandas >= 1.3.0
- numpy >= 1.21.0
- matplotlib >= 3.4.0
- seaborn >= 0.11.0

## 错误处理

脚本包含完善的错误处理机制：
- 自动检测缺失的依赖包
- 验证数据文件存在性
- 处理数据格式异常
- 提供详细的错误信息和建议
