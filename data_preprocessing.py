#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗数据预处理脚本
处理医疗咨询平台的各类数据文件
"""

import pandas as pd
import numpy as np
import os
import warnings
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体和忽略警告
try:
    # Windows系统常用中文字体
    import platform
    if platform.system() == 'Windows':
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'FangSong']
    else:
        # Linux/Mac系统
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'Noto Sans CJK SC']
except:
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

class MedicalDataPreprocessor:
    def __init__(self, data_dir='.'):
        """
        初始化数据预处理器
        
        Args:
            data_dir (str): 数据文件所在目录
        """
        self.data_dir = data_dir
        self.data = {}
        self.processed_data = {}
        
    def load_data(self):
        """加载所有CSV文件"""
        print("正在加载数据文件...")
        
        # 定义文件列表
        files = [
            'dim_patient.csv',
            'dim_doctor.csv', 
            'dim_hospital.csv',
            'dim_medicine.csv',
            'dim_user.csv',
            'fact_consultation.csv',
            'fact_payment.csv',
            'fact_prescription.csv',
            'fact_prescription_detail.csv'
        ]
        
        for file in files:
            file_path = os.path.join(self.data_dir, file)
            if os.path.exists(file_path):
                try:
                    df = pd.read_csv(file_path, encoding='utf-8')
                    table_name = file.replace('.csv', '')
                    self.data[table_name] = df
                    print(f"✓ 成功加载 {file}: {df.shape[0]} 行, {df.shape[1]} 列")
                except Exception as e:
                    print(f"✗ 加载 {file} 失败: {e}")
            else:
                print(f"✗ 文件不存在: {file}")
    
    def basic_info(self):
        """显示数据基本信息"""
        print("\n" + "="*50)
        print("数据基本信息")
        print("="*50)
        
        for table_name, df in self.data.items():
            print(f"\n【{table_name}】")
            print(f"形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            print(f"缺失值: {df.isnull().sum().sum()}")
            print(f"重复行: {df.duplicated().sum()}")
    
    def clean_patient_data(self):
        """清洗患者数据"""
        print("\n正在清洗患者数据...")
        df = self.data['dim_patient'].copy()
        
        # 处理年龄异常值
        df['age'] = pd.to_numeric(df['age'], errors='coerce')
        df = df[(df['age'] >= 0) & (df['age'] <= 120)]
        
        # 处理性别数据
        df['gender'] = df['gender'].map({'男': 'M', '女': 'F'})
        
        # 处理日期格式
        df['registration_date'] = pd.to_datetime(df['registration_date'], errors='coerce')
        
        # 处理布尔值
        df['is_chronic_disease'] = df['is_chronic_disease'].map({True: 1, False: 0})
        
        # 处理缺失值
        df['medical_insurance_type'].fillna('无', inplace=True)
        df['allergies'].fillna('无', inplace=True)
        
        self.processed_data['dim_patient'] = df
        print(f"✓ 患者数据清洗完成，保留 {len(df)} 条记录")
    
    def clean_doctor_data(self):
        """清洗医生数据"""
        print("正在清洗医生数据...")
        df = self.data['dim_doctor'].copy()
        
        # 处理年龄和工作年限
        df['age'] = pd.to_numeric(df['age'], errors='coerce')
        df['years_of_experience'] = pd.to_numeric(df['years_of_experience'], errors='coerce')
        
        # 处理咨询费用
        df['consultation_fee'] = pd.to_numeric(df['consultation_fee'], errors='coerce')
        
        # 处理评分
        df['rating'] = pd.to_numeric(df['rating'], errors='coerce')
        df['rating'] = df['rating'].clip(0, 5)  # 限制评分在0-5之间
        
        # 处理日期
        df['created_at'] = pd.to_datetime(df['created_at'], errors='coerce')
        
        # 处理布尔值
        df['is_accepting_new_patients'] = df['is_accepting_new_patients'].map({True: 1, False: 0})
        
        self.processed_data['dim_doctor'] = df
        print(f"✓ 医生数据清洗完成，保留 {len(df)} 条记录")
    
    def clean_consultation_data(self):
        """清洗咨询数据"""
        print("正在清洗咨询数据...")
        df = self.data['fact_consultation'].copy()
        
        # 处理日期时间
        df['consultation_date'] = pd.to_datetime(df['consultation_date'], errors='coerce')
        df['created_at'] = pd.to_datetime(df['created_at'], errors='coerce')
        
        # 处理数值字段
        numeric_cols = ['duration_minutes', 'actual_fee', 'insurance_covered_amount', 'patient_paid_amount']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理评分
        df['rating'] = pd.to_numeric(df['rating'], errors='coerce')
        df['rating'] = df['rating'].clip(1, 5)  # 限制评分在1-5之间
        
        # 处理布尔值
        df['is_follow_up'] = df['is_follow_up'].map({True: 1, False: 0})
        
        # 创建衍生字段
        df['consultation_hour'] = df['consultation_date'].dt.hour
        df['consultation_weekday'] = df['consultation_date'].dt.dayofweek
        df['consultation_month'] = df['consultation_date'].dt.month
        
        self.processed_data['fact_consultation'] = df
        print(f"✓ 咨询数据清洗完成，保留 {len(df)} 条记录")
    
    def generate_statistics(self):
        """生成统计报告"""
        print("\n" + "="*50)
        print("数据统计报告")
        print("="*50)
        
        # 患者统计
        if 'dim_patient' in self.processed_data:
            patient_df = self.processed_data['dim_patient']
            print(f"\n【患者统计】")
            print(f"总患者数: {len(patient_df)}")
            print(f"性别分布: {patient_df['gender'].value_counts().to_dict()}")
            print(f"平均年龄: {patient_df['age'].mean():.1f} 岁")
            print(f"慢性病患者比例: {patient_df['is_chronic_disease'].mean():.2%}")
        
        # 医生统计
        if 'dim_doctor' in self.processed_data:
            doctor_df = self.processed_data['dim_doctor']
            print(f"\n【医生统计】")
            print(f"总医生数: {len(doctor_df)}")
            print(f"平均工作年限: {doctor_df['years_of_experience'].mean():.1f} 年")
            print(f"平均咨询费: {doctor_df['consultation_fee'].mean():.2f} 元")
            print(f"平均评分: {doctor_df['rating'].mean():.2f}")
        
        # 咨询统计
        if 'fact_consultation' in self.processed_data:
            consult_df = self.processed_data['fact_consultation']
            print(f"\n【咨询统计】")
            print(f"总咨询数: {len(consult_df)}")
            print(f"咨询类型分布: {consult_df['consultation_type'].value_counts().to_dict()}")
            print(f"平均咨询时长: {consult_df['duration_minutes'].mean():.1f} 分钟")
            print(f"平均费用: {consult_df['actual_fee'].mean():.2f} 元")
    
    def save_processed_data(self, output_dir='processed_data'):
        """保存处理后的数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        print(f"\n正在保存处理后的数据到 {output_dir} 目录...")
        
        for table_name, df in self.processed_data.items():
            output_path = os.path.join(output_dir, f"{table_name}_processed.csv")
            df.to_csv(output_path, index=False, encoding='utf-8')
            print(f"✓ 保存 {table_name}: {output_path}")
    
    def run_preprocessing(self):
        """运行完整的数据预处理流程"""
        print("开始数据预处理...")
        
        # 加载数据
        self.load_data()
        
        # 显示基本信息
        self.basic_info()
        
        # 清洗各类数据
        if 'dim_patient' in self.data:
            self.clean_patient_data()
        
        if 'dim_doctor' in self.data:
            self.clean_doctor_data()
            
        if 'fact_consultation' in self.data:
            self.clean_consultation_data()
        
        # 生成统计报告
        self.generate_statistics()
        
        # 保存处理后的数据
        self.save_processed_data()
        
        print("\n数据预处理完成！")

def main():
    """主函数"""
    # 创建预处理器实例
    preprocessor = MedicalDataPreprocessor()
    
    # 运行预处理
    preprocessor.run_preprocessing()

if __name__ == "__main__":
    main()
