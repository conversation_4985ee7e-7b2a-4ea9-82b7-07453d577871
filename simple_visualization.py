#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版数据可视化脚本
专门解决中文字体显示问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import warnings

warnings.filterwarnings('ignore')

def setup_font():
    """设置字体"""
    import matplotlib.font_manager as fm
    
    # 获取系统字体
    font_list = [f.name for f in fm.fontManager.ttflist]
    
    # 中文字体候选
    chinese_fonts = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'FangSong', 'STSong']
    
    # 找到可用字体
    available_font = None
    for font in chinese_fonts:
        if font in font_list:
            available_font = font
            break
    
    if available_font:
        plt.rcParams['font.sans-serif'] = [available_font]
        print(f"使用字体: {available_font}")
    else:
        # 使用默认字体，但设置为支持Unicode
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        print("使用默认字体")
    
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10

def load_data():
    """加载数据"""
    data = {}
    
    files = {
        'patients': 'dim_patient.csv',
        'doctors': 'dim_doctor.csv',
        'consultations': 'fact_consultation.csv'
    }
    
    for name, filename in files.items():
        if os.path.exists(filename):
            try:
                df = pd.read_csv(filename, encoding='utf-8')
                data[name] = df
                print(f"✓ 加载 {filename}: {df.shape}")
            except Exception as e:
                print(f"✗ 加载 {filename} 失败: {e}")
    
    return data

def create_patient_charts(df):
    """创建患者数据图表"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Patient Data Analysis', fontsize=14, y=0.95)
    
    # 1. 年龄分布
    axes[0, 0].hist(df['age'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('Age Distribution')
    axes[0, 0].set_xlabel('Age')
    axes[0, 0].set_ylabel('Count')
    
    # 2. 性别分布
    gender_counts = df['gender'].value_counts()
    labels = ['Male' if x == '男' else 'Female' if x == '女' else str(x) for x in gender_counts.index]
    axes[0, 1].pie(gender_counts.values, labels=labels, autopct='%1.1f%%', 
                   colors=['lightblue', 'lightpink'])
    axes[0, 1].set_title('Gender Distribution')
    
    # 3. 血型分布
    blood_counts = df['blood_type'].value_counts()
    axes[1, 0].bar(blood_counts.index, blood_counts.values, color='lightgreen', alpha=0.7)
    axes[1, 0].set_title('Blood Type Distribution')
    axes[1, 0].set_xlabel('Blood Type')
    axes[1, 0].set_ylabel('Count')
    
    # 4. 医保类型分布
    insurance_counts = df['medical_insurance_type'].value_counts().head(5)
    axes[1, 1].barh(range(len(insurance_counts)), insurance_counts.values, color='orange', alpha=0.7)
    axes[1, 1].set_yticks(range(len(insurance_counts)))
    axes[1, 1].set_yticklabels([str(x)[:10] + '...' if len(str(x)) > 10 else str(x) for x in insurance_counts.index])
    axes[1, 1].set_title('Insurance Type Distribution')
    axes[1, 1].set_xlabel('Count')
    
    plt.tight_layout()
    return fig

def create_doctor_charts(df):
    """创建医生数据图表"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Doctor Data Analysis', fontsize=14, y=0.95)
    
    # 1. 科室分布
    dept_counts = df['department'].value_counts().head(8)
    axes[0, 0].barh(range(len(dept_counts)), dept_counts.values, color='lightcoral')
    axes[0, 0].set_yticks(range(len(dept_counts)))
    axes[0, 0].set_yticklabels([str(x)[:8] + '...' if len(str(x)) > 8 else str(x) for x in dept_counts.index])
    axes[0, 0].set_title('Department Distribution')
    axes[0, 0].set_xlabel('Count')
    
    # 2. 职称分布
    title_counts = df['title'].value_counts()
    axes[0, 1].pie(title_counts.values, labels=[str(x)[:6] for x in title_counts.index], 
                   autopct='%1.1f%%')
    axes[0, 1].set_title('Title Distribution')
    
    # 3. 工作年限vs咨询费用
    axes[1, 0].scatter(df['years_of_experience'], df['consultation_fee'], alpha=0.6, color='purple')
    axes[1, 0].set_title('Experience vs Fee')
    axes[1, 0].set_xlabel('Years of Experience')
    axes[1, 0].set_ylabel('Consultation Fee')
    
    # 4. 评分分布
    axes[1, 1].hist(df['rating'], bins=20, alpha=0.7, color='gold', edgecolor='black')
    axes[1, 1].set_title('Rating Distribution')
    axes[1, 1].set_xlabel('Rating')
    axes[1, 1].set_ylabel('Count')
    
    plt.tight_layout()
    return fig

def create_consultation_charts(df):
    """创建咨询数据图表"""
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('Consultation Data Analysis', fontsize=14, y=0.95)
    
    # 1. 咨询类型分布
    type_counts = df['consultation_type'].value_counts()
    axes[0, 0].pie(type_counts.values, labels=[str(x)[:6] for x in type_counts.index], 
                   autopct='%1.1f%%')
    axes[0, 0].set_title('Consultation Type')
    
    # 2. 状态分布
    status_counts = df['status'].value_counts()
    axes[0, 1].bar(range(len(status_counts)), status_counts.values, 
                   color=['green', 'orange', 'red', 'blue', 'purple'][:len(status_counts)])
    axes[0, 1].set_xticks(range(len(status_counts)))
    axes[0, 1].set_xticklabels([str(x)[:4] for x in status_counts.index], rotation=45)
    axes[0, 1].set_title('Status Distribution')
    
    # 3. 时长分布
    axes[0, 2].hist(df['duration_minutes'], bins=30, alpha=0.7, color='lightblue', edgecolor='black')
    axes[0, 2].set_title('Duration Distribution')
    axes[0, 2].set_xlabel('Minutes')
    axes[0, 2].set_ylabel('Count')
    
    # 4. 每小时咨询量
    df['consultation_date'] = pd.to_datetime(df['consultation_date'], errors='coerce')
    df['hour'] = df['consultation_date'].dt.hour
    hourly = df['hour'].value_counts().sort_index()
    axes[1, 0].plot(hourly.index, hourly.values, marker='o', linewidth=2, color='red')
    axes[1, 0].set_title('Hourly Consultations')
    axes[1, 0].set_xlabel('Hour')
    axes[1, 0].set_ylabel('Count')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 每周咨询量
    df['weekday'] = df['consultation_date'].dt.dayofweek
    weekly = df['weekday'].value_counts().sort_index()
    weekday_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    axes[1, 1].bar(range(len(weekly)), weekly.values, color='lightgreen')
    axes[1, 1].set_xticks(range(len(weekday_names)))
    axes[1, 1].set_xticklabels(weekday_names)
    axes[1, 1].set_title('Weekly Consultations')
    
    # 6. 费用分布
    axes[1, 2].hist(df['actual_fee'], bins=30, alpha=0.7, color='gold', edgecolor='black')
    axes[1, 2].set_title('Fee Distribution')
    axes[1, 2].set_xlabel('Fee')
    axes[1, 2].set_ylabel('Count')
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("简化版数据可视化工具")
    print("="*40)
    
    # 设置字体
    setup_font()
    
    # 加载数据
    data = load_data()
    
    if not data:
        print("没有找到数据文件")
        return
    
    # 创建输出目录
    output_dir = 'simple_charts'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成图表
    if 'patients' in data:
        print("生成患者数据图表...")
        fig = create_patient_charts(data['patients'])
        fig.savefig(os.path.join(output_dir, 'patient_analysis.png'), dpi=300, bbox_inches='tight')
        plt.show()
        plt.close()
    
    if 'doctors' in data:
        print("生成医生数据图表...")
        fig = create_doctor_charts(data['doctors'])
        fig.savefig(os.path.join(output_dir, 'doctor_analysis.png'), dpi=300, bbox_inches='tight')
        plt.show()
        plt.close()
    
    if 'consultations' in data:
        print("生成咨询数据图表...")
        fig = create_consultation_charts(data['consultations'])
        fig.savefig(os.path.join(output_dir, 'consultation_analysis.png'), dpi=300, bbox_inches='tight')
        plt.show()
        plt.close()
    
    print(f"\n图表已保存到 {output_dir} 目录")
    print("可视化完成！")

if __name__ == "__main__":
    main()
