#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗数据可视化分析脚本
对预处理后的数据进行可视化分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
import warnings

# 设置中文字体和样式
import matplotlib

# 导入字体配置
from font_config import setup_chinese_font
setup_chinese_font()

matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

# 尝试设置中文字体
try:
    # Windows系统常用中文字体
    import platform
    if platform.system() == 'Windows':
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'FangSong']
    else:
        # Linux/Mac系统
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'Noto Sans CJK SC']
except:
    pass

# 设置样式
try:
    plt.style.use('seaborn-v0_8')
except:
    try:
        plt.style.use('seaborn')
    except:
        pass

warnings.filterwarnings('ignore')

def setup_chinese_font():
    """设置中文字体显示"""
    import matplotlib.font_manager as fm

    # 获取系统中可用的字体
    font_list = [f.name for f in fm.fontManager.ttflist]

    # 中文字体优先级列表
    chinese_fonts = [
        'Microsoft YaHei',
        'SimHei',
        'KaiTi',
        'FangSong',
        'STSong',
        'STKaiti',
        'STFangsong',
        'Noto Sans CJK SC',
        'WenQuanYi Micro Hei',
        'Arial Unicode MS',
        'DejaVu Sans'
    ]

    # 找到第一个可用的中文字体
    available_font = None
    for font in chinese_fonts:
        if font in font_list:
            available_font = font
            break

    if available_font:
        plt.rcParams['font.sans-serif'] = [available_font]
        print(f"使用字体: {available_font}")
    else:
        print("警告: 未找到合适的中文字体，可能无法正确显示中文")
        # 使用默认设置
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

    plt.rcParams['axes.unicode_minus'] = False
    return available_font

class MedicalDataVisualizer:
    def __init__(self, data_dir='processed_data'):
        """
        初始化数据可视化器

        Args:
            data_dir (str): 处理后数据文件所在目录
        """
        self.data_dir = data_dir
        self.data = {}
        self.output_dir = 'visualizations'

        # 设置中文字体
        self.font_name = setup_chinese_font()

        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_processed_data(self):
        """加载处理后的数据"""
        print("正在加载处理后的数据...")
        
        files = [
            'dim_patient_processed.csv',
            'dim_doctor_processed.csv',
            'fact_consultation_processed.csv'
        ]
        
        for file in files:
            file_path = os.path.join(self.data_dir, file)
            if os.path.exists(file_path):
                try:
                    df = pd.read_csv(file_path, encoding='utf-8')
                    table_name = file.replace('_processed.csv', '')
                    self.data[table_name] = df
                    print(f"✓ 成功加载 {file}: {df.shape}")
                except Exception as e:
                    print(f"✗ 加载 {file} 失败: {e}")
    
    def plot_patient_analysis(self):
        """患者数据分析可视化"""
        if 'dim_patient' not in self.data:
            return
        
        df = self.data['dim_patient']
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('患者数据分析', fontsize=16, fontweight='bold')
        
        # 1. 年龄分布
        axes[0, 0].hist(df['age'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('患者年龄分布', fontsize=12, pad=20)
        axes[0, 0].set_xlabel('年龄', fontsize=10)
        axes[0, 0].set_ylabel('人数', fontsize=10)
        
        # 2. 性别分布
        gender_counts = df['gender'].value_counts()
        gender_labels = []
        for gender in gender_counts.index:
            if gender == '男' or gender == 'M':
                gender_labels.append('男性')
            elif gender == '女' or gender == 'F':
                gender_labels.append('女性')
            else:
                gender_labels.append(str(gender))

        axes[0, 1].pie(gender_counts.values, labels=gender_labels, autopct='%1.1f%%',
                       colors=['lightblue', 'lightpink'], textprops={'fontsize': 10})
        axes[0, 1].set_title('患者性别分布', fontsize=12, pad=20)
        
        # 3. 血型分布
        blood_type_counts = df['blood_type'].value_counts()
        axes[1, 0].bar(blood_type_counts.index, blood_type_counts.values,
                       color='lightgreen', alpha=0.7)
        axes[1, 0].set_title('血型分布', fontsize=12, pad=20)
        axes[1, 0].set_xlabel('血型', fontsize=10)
        axes[1, 0].set_ylabel('人数', fontsize=10)

        # 4. 医保类型分布
        insurance_counts = df['medical_insurance_type'].value_counts()
        axes[1, 1].barh(insurance_counts.index, insurance_counts.values,
                        color='orange', alpha=0.7)
        axes[1, 1].set_title('医保类型分布', fontsize=12, pad=20)
        axes[1, 1].set_xlabel('人数', fontsize=10)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'patient_analysis.png'), dpi=300, bbox_inches='tight')
        plt.show()
        print("✓ 患者分析图表已保存")
    
    def plot_doctor_analysis(self):
        """医生数据分析可视化"""
        if 'dim_doctor' not in self.data:
            return
        
        df = self.data['dim_doctor']
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('医生数据分析', fontsize=16, fontweight='bold')
        
        # 1. 科室分布
        dept_counts = df['department'].value_counts().head(10)
        axes[0, 0].barh(dept_counts.index, dept_counts.values, color='lightcoral')
        axes[0, 0].set_title('科室分布（前10）')
        axes[0, 0].set_xlabel('医生数量')
        
        # 2. 职称分布
        title_counts = df['title'].value_counts()
        axes[0, 1].pie(title_counts.values, labels=title_counts.index, autopct='%1.1f%%')
        axes[0, 1].set_title('职称分布')
        
        # 3. 工作年限vs咨询费用散点图
        axes[1, 0].scatter(df['years_of_experience'], df['consultation_fee'], 
                          alpha=0.6, color='purple')
        axes[1, 0].set_title('工作年限 vs 咨询费用')
        axes[1, 0].set_xlabel('工作年限（年）')
        axes[1, 0].set_ylabel('咨询费用（元）')
        
        # 4. 评分分布
        axes[1, 1].hist(df['rating'], bins=20, alpha=0.7, color='gold', edgecolor='black')
        axes[1, 1].set_title('医生评分分布')
        axes[1, 1].set_xlabel('评分')
        axes[1, 1].set_ylabel('医生数量')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'doctor_analysis.png'), dpi=300, bbox_inches='tight')
        plt.show()
        print("✓ 医生分析图表已保存")
    
    def plot_consultation_analysis(self):
        """咨询数据分析可视化"""
        if 'fact_consultation' not in self.data:
            return
        
        df = self.data['fact_consultation']
        
        # 创建子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('咨询数据分析', fontsize=16, fontweight='bold')
        
        # 1. 咨询类型分布
        consult_type_counts = df['consultation_type'].value_counts()
        axes[0, 0].pie(consult_type_counts.values, labels=consult_type_counts.index, 
                       autopct='%1.1f%%')
        axes[0, 0].set_title('咨询类型分布')
        
        # 2. 咨询状态分布
        status_counts = df['status'].value_counts()
        axes[0, 1].bar(status_counts.index, status_counts.values, 
                       color=['green', 'orange', 'red', 'blue', 'purple'])
        axes[0, 1].set_title('咨询状态分布')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 咨询时长分布
        axes[0, 2].hist(df['duration_minutes'], bins=30, alpha=0.7, 
                       color='lightblue', edgecolor='black')
        axes[0, 2].set_title('咨询时长分布')
        axes[0, 2].set_xlabel('时长（分钟）')
        axes[0, 2].set_ylabel('咨询次数')
        
        # 4. 每小时咨询量
        hourly_consults = df['consultation_hour'].value_counts().sort_index()
        axes[1, 0].plot(hourly_consults.index, hourly_consults.values, 
                       marker='o', linewidth=2, color='red')
        axes[1, 0].set_title('每小时咨询量')
        axes[1, 0].set_xlabel('小时')
        axes[1, 0].set_ylabel('咨询次数')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 每周咨询量
        weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        weekly_consults = df['consultation_weekday'].value_counts().sort_index()
        axes[1, 1].bar(range(len(weekly_consults)), weekly_consults.values, 
                      color='lightgreen')
        axes[1, 1].set_title('每周咨询量')
        axes[1, 1].set_xticks(range(len(weekday_names)))
        axes[1, 1].set_xticklabels(weekday_names)
        axes[1, 1].set_ylabel('咨询次数')
        
        # 6. 费用分布
        axes[1, 2].hist(df['actual_fee'], bins=30, alpha=0.7, 
                       color='gold', edgecolor='black')
        axes[1, 2].set_title('咨询费用分布')
        axes[1, 2].set_xlabel('费用（元）')
        axes[1, 2].set_ylabel('咨询次数')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'consultation_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
        print("✓ 咨询分析图表已保存")
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print("\n" + "="*60)
        print("数据可视化分析报告")
        print("="*60)
        
        # 患者数据摘要
        if 'dim_patient' in self.data:
            patient_df = self.data['dim_patient']
            print(f"\n【患者数据摘要】")
            print(f"• 总患者数: {len(patient_df):,}")
            print(f"• 平均年龄: {patient_df['age'].mean():.1f} 岁")
            print(f"• 年龄范围: {patient_df['age'].min()}-{patient_df['age'].max()} 岁")
            print(f"• 慢性病患者比例: {patient_df['is_chronic_disease'].mean():.1%}")
        
        # 医生数据摘要
        if 'dim_doctor' in self.data:
            doctor_df = self.data['dim_doctor']
            print(f"\n【医生数据摘要】")
            print(f"• 总医生数: {len(doctor_df):,}")
            print(f"• 平均工作年限: {doctor_df['years_of_experience'].mean():.1f} 年")
            print(f"• 平均咨询费: {doctor_df['consultation_fee'].mean():.2f} 元")
            print(f"• 平均评分: {doctor_df['rating'].mean():.2f}/5.0")
        
        # 咨询数据摘要
        if 'fact_consultation' in self.data:
            consult_df = self.data['fact_consultation']
            print(f"\n【咨询数据摘要】")
            print(f"• 总咨询数: {len(consult_df):,}")
            print(f"• 平均咨询时长: {consult_df['duration_minutes'].mean():.1f} 分钟")
            print(f"• 平均咨询费用: {consult_df['actual_fee'].mean():.2f} 元")
            print(f"• 完成率: {(consult_df['status'] == '已完成').mean():.1%}")
    
    def run_visualization(self):
        """运行完整的可视化分析"""
        print("开始数据可视化分析...")
        
        # 加载数据
        self.load_processed_data()
        
        # 生成各类图表
        self.plot_patient_analysis()
        self.plot_doctor_analysis()
        self.plot_consultation_analysis()
        
        # 生成汇总报告
        self.generate_summary_report()
        
        print(f"\n所有图表已保存到 {self.output_dir} 目录")
        print("数据可视化分析完成！")

def main():
    """主函数"""
    # 创建可视化器实例
    visualizer = MedicalDataVisualizer()
    
    # 运行可视化分析
    visualizer.run_visualization()

if __name__ == "__main__":
    main()
