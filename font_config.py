# matplotlib中文字体配置
# 由fix_chinese_font.py自动生成

import matplotlib.pyplot as plt
import platform

def setup_chinese_font():
    """设置中文字体"""
    try:
        # 推荐字体: Microsoft Sans Serif
        if platform.system() == 'Windows':
            plt.rcParams['font.sans-serif'] = ['Microsoft Sans Serif', 'Microsoft YaHei', 'SimHei', 'KaiTi']
        else:
            plt.rcParams['font.sans-serif'] = ['Microsoft Sans Serif', 'DejaVu Sans', 'Arial Unicode MS']
        
        plt.rcParams['axes.unicode_minus'] = False
        print(f"已设置中文字体: Microsoft Sans Serif")
        
    except Exception as e:
        print(f"字体设置失败: {e}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

# 自动执行字体设置
setup_chinese_font()
