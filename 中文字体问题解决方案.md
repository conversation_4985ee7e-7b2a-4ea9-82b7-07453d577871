# 中文字体显示问题解决方案

## 问题描述
在matplotlib生成的图表中，中文文字显示为方框（□□□），这是因为matplotlib默认字体不支持中文字符。

## 解决方案

### 方案1：使用字体修复脚本（推荐）

运行字体修复脚本：
```bash
python fix_chinese_font.py
```

这个脚本会：
1. 自动检测系统中可用的中文字体
2. 生成测试图表验证字体效果
3. 创建字体配置文件
4. 修复现有的可视化脚本

### 方案2：使用简化版可视化

如果字体问题无法解决，可以使用英文版本：
```bash
python simple_visualization.py
```

这个版本使用英文标签，完全避免中文显示问题。

### 方案3：手动配置字体

#### Windows系统
1. 确保系统安装了中文字体（通常已安装）
2. 在Python脚本开头添加：
```python
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
```

#### Linux系统
1. 安装中文字体：
```bash
sudo apt-get install fonts-wqy-microhei
```
2. 在Python脚本中设置：
```python
plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans']
```

#### Mac系统
1. 系统通常已包含中文字体
2. 设置字体：
```python
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Heiti TC']
```

### 方案4：使用批处理文件

双击运行 `run_analysis.bat` 文件，选择"简化分析"选项。

## 验证字体设置

运行以下代码测试中文显示：
```python
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 列出可用字体
fonts = [f.name for f in fm.fontManager.ttflist if 'YaHei' in f.name or 'SimHei' in f.name]
print("可用中文字体:", fonts)

# 测试中文显示
plt.figure(figsize=(8, 6))
plt.plot([1, 2, 3], [1, 4, 2])
plt.title('中文测试标题')
plt.xlabel('横轴标签')
plt.ylabel('纵轴标签')
plt.show()
```

## 常见问题

### Q: 运行脚本后仍然显示方框
A: 尝试以下步骤：
1. 重启Python环境
2. 清除matplotlib缓存：删除 `~/.matplotlib` 目录
3. 使用简化版可视化脚本

### Q: 提示找不到字体
A: 
1. 检查系统是否安装了中文字体
2. 使用 `fc-list :lang=zh` (Linux) 或字体管理器查看可用字体
3. 手动下载安装中文字体

### Q: 图表保存后中文正常，但显示时是方框
A: 这是显示器字体渲染问题，保存的图片是正确的。

## 推荐字体

- **Windows**: Microsoft YaHei, SimHei
- **Linux**: WenQuanYi Micro Hei, Noto Sans CJK SC
- **Mac**: Arial Unicode MS, Heiti TC

## 最终建议

1. 首先尝试运行 `python fix_chinese_font.py`
2. 如果仍有问题，使用 `python simple_visualization.py`
3. 对于生产环境，建议使用英文标签避免字体兼容性问题
